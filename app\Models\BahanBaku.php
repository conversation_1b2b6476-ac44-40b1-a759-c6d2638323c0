<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class BahanBaku extends Model
{
    use HasFactory;

    protected $fillable = [
        'kode',
        'nama',
        'bobot_wma_1',
        'bobot_wma_2',
        'bobot_wma_3',
    ];

    public function stoks(): HasMany
    {
        return $this->hasMany(Stok::class);
    }

    public function produks(): BelongsToMany
    {
        return $this->belongsToMany(Produk::class, 'bahan_baku_produk');
    }
}