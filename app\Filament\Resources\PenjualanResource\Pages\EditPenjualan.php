<?php

namespace App\Filament\Resources\PenjualanResource\Pages;

use App\Filament\Resources\PenjualanResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\Log;

class EditPenjualan extends EditRecord
{
    protected static string $resource = PenjualanResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->label('Hapus')
                ->modalHeading('Hapus Data Penjualan')
                ->modalDescription('Apakah Anda yakin ingin menghapus data ini?')
                ->modalSubmitActionLabel('Ya, Hapus')
                ->modalCancelActionLabel('Batal'),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

     public function getTitle(): string
    {
        return 'Edit Data Penjualan';
    }

    protected function getSaveFormAction(): Actions\Action
    {
        return parent::getSaveFormAction()
            ->label('Simpan');
    }

     protected function getCancelFormAction(): Actions\Action
    {
        return parent::getCancelFormAction()
            ->label('Batal');
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $relatedProduks = $this->record->produks;
        $produksForRepeater = [];
        foreach ($relatedProduks as $produk) {
            $produksForRepeater[] = [
                'produk_id' => $produk->id,
                'harga' => $produk->pivot->harga,
                'jumlah' => $produk->pivot->jumlah,
                'subtotal' => $produk->pivot->subtotal,
            ];
        }
        $data['produks'] = $produksForRepeater;

        // Fill total_harga from the record
        $data['total_harga'] = $this->record->total_harga;

        Log::info('Data mutated before fill:', $data);
        return $data;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Calculate total_harga from the repeater data before modifying it
        $data['total_harga'] = PenjualanResource::calculateTotal($data['produks'] ?? []);
        Log::info('Calculated total before save:', ['total' => $data['total_harga']]);
        return $data;
    }

    protected function afterSave(): void
    {
        $produksDataFromForm = $this->form->getState()['produks'] ?? [];
        $produksPivotData = [];

        if (is_array($produksDataFromForm)) {
            foreach ($produksDataFromForm as $item) {
                if (isset($item['produk_id']) && !empty($item['produk_id']) && isset($item['harga']) && isset($item['jumlah'])) {
                    $harga = is_numeric($item['harga']) ? (float)$item['harga'] : 0;
                    $jumlah = is_numeric($item['jumlah']) ? (int)$item['jumlah'] : 0;
                    $subtotal = $harga * $jumlah; // Calculate subtotal

                    $produksPivotData[$item['produk_id']] = [
                        'harga' => $harga,
                        'jumlah' => $jumlah,
                        'subtotal' => $subtotal, // Add subtotal to pivot data
                    ];
                } else {
                     Log::warning('Skipping invalid repeater item during sync after save:', $item);
                }
            }
        }

        Log::info('Syncing produks after save (with subtotal):', $produksPivotData);
        $this->record->produks()->sync($produksPivotData);
        // Ensure total is zero if no products after sync
        if (empty($produksPivotData) && $this->record->total_harga != 0) {
            $this->record->update(['total_harga' => 0]);
        }
    }
}