<?php

namespace Database\Seeders;

use App\Models\BahanBaku;
use App\Models\Produk;
use Illuminate\Database\Seeder;

class ProdukSeeder extends Seeder
{
    public function run(): void
    {
        $bahanBakuParisKeju = BahanBaku::whereIn('nama', [
            '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Ra<PERSON>', 'Telur', 'Tepung Terigu'
        ])->pluck('id');

        $rotiParisKeju = Produk::create([
            'nama_produk' => 'Roti Paris Keju',
            'harga' => 3000,
            'status' => 'Aktif',
        ]);

        $rotiParisKeju->bahanBakus()->attach($bahanBakuParisKeju);

        $bahanBakuSisir = BahanBaku::whereIn('nama', [
            'Gula', 'Margarin', 'Me<PERSON>', 'Ragi', 'Telur', 'Tepung Terigu'
        ])->pluck('id');

        $rotiSisir = Produk::create([
            'nama_produk' => 'Roti Sisir',
            'harga' => 3500,
            'status' => 'Aktif',
        ]);

        $rotiSisir->bahanBakus()->attach($bahanBakuSisir);
    }
}