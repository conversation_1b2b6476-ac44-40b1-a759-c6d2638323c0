<?php

namespace App\Http\Controllers;

use App\Models\BahanBaku;
use App\Models\Penjualan;
use App\Models\Prediksi;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;

class PdfController extends Controller
{
    public function generatePenjualanPdf(Penjualan $penjualan)
    {
        $penjualan->load('produks'); // Load related products
        $pdf = Pdf::loadView('pdf.penjualan', compact('penjualan')); // Load the view
        $pdf->setPaper('A4', 'portrait'); // Set paper size and orientation
        return $pdf->stream('Invoice Penjualan - ' . $penjualan->kode_pesanan . '.pdf'); // Stream the generated PDF for viewing
    }

    public function generatePrediksiPdf(Prediksi $prediksi)
    {
        $bahanBaku = $prediksi->bahanBaku;
        if (!$bahanBaku) {
            return redirect()->back()->with('error', 'Bahan baku terkait tidak ditemukan.');
        }

        // Prepare data for the PDF view from the Prediksi model
        $data = [
            'prediction' => $prediksi->hasil_prediksi,
            'periode' => $prediksi->periode, // This is already a Carbon instance
            'mape' => $prediksi->mape,
            'predictions' => $prediksi->detail_prediksi, // This is the stored JSON array
            'satuan' => $prediksi->satuan
        ];

        $pdf = PDF::loadView('pdf.prediksi', [
            'data' => $data,
            'bahanBaku' => $bahanBaku
        ]);
        $pdf->setPaper('A4', 'portrait');

        $periodeFormatted = $prediksi->periode ? $prediksi->periode->translatedFormat('F Y') : 'periode-tidak-diketahui';
        $fileName = 'Laporan Prediksi Stok ' . str_replace(' ', '_', $bahanBaku->nama) . ' Periode ' . $periodeFormatted . '.pdf';

        return $pdf->stream($fileName);
    }

    public function generatePrediksiPreviewPdf($bahanBakuId)
    {
        $bahanBaku = BahanBaku::find($bahanBakuId);
        if (!$bahanBaku) {
            return redirect()->back()->with('error', 'Bahan baku tidak ditemukan.');
        }

        $result = Prediksi::calculatePrediction($bahanBakuId);

        if (!$result['success']) {
            return redirect()->back()->with('error', $result['message']);
        }

        // Prepare data for the PDF view from the calculation result
        $data = [
            'prediction' => $result['data']['prediction'],
            'periode' => $result['data']['periode'], // This is already a Carbon instance
            'mape' => $result['data']['mape'],
            'predictions' => $result['data']['predictions'], // This is the array of historical and predicted values
            'satuan' => $result['data']['satuan']
        ];

        $pdf = PDF::loadView('pdf.prediksi', [
            'data' => $data,
            'bahanBaku' => $bahanBaku
        ]);
        $pdf->setPaper('A4', 'portrait');

        $periodeFormatted = $result['data']['periode'] ? $result['data']['periode']->translatedFormat('F Y') : 'periode-tidak-diketahui';
        $fileName = 'Laporan Prediksi Stok ' . str_replace(' ', '_', $bahanBaku->nama) . ' Periode ' . $periodeFormatted . '.pdf';

        return $pdf->stream($fileName);
    }
}