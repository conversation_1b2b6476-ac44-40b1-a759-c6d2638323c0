<?php

namespace App\Filament\Resources\PrediksiResource\Pages;

use App\Filament\Resources\PrediksiResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListPrediksis extends ListRecords
{
    protected static string $resource = PrediksiResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('Buat Prediksi Stok'),
        ];
    }

    public function getTabs(): array
    {
        return [
            'stok' => ListRecords\Tab::make('Stok')
                ->icon('heroicon-o-archive-box')
                ->modifyQueryUsing(fn (Builder $query) => $query),
            'produksi' => ListRecords\Tab::make('Produksi')
                ->icon('heroicon-o-rectangle-stack')
        ];
    }

    public function getDefaultActiveTab(): string | int | null
    {
        return 'stok';
    }
}