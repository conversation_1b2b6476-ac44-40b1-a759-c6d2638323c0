<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Produksi extends Model
{
    use HasFactory;

    protected $fillable = [
        'produk_id',
        'periode',
        'jumlah',
        'satuan',
    ];

    protected $casts = [
        'periode' => 'date',
        'jumlah' => 'decimal:1',
    ];

    public function produk(): BelongsTo
    {
        return $this->belongsTo(Produk::class);
    }

    public static function createFromProduksiData($periode, $produksiData)
    {
        foreach ($produksiData as $produkId => $data) {
            self::updateOrCreate(
                [
                    'periode' => $periode,
                    'produk_id' => $produkId
                ],
                [
                    'jumlah' => $data['jumlah'] ?? 0.0,
                    'satuan' => $data['satuan'] ?? 'pcs',
                ]
            );
        }
    }

    protected static function boot()
    {
        parent::boot();

        static::saving(function ($model) {
            if (request()->has('produksi_data')) {
                $periode = $model->periode;
                self::createFromProduksiData($periode, request()->produksi_data);
                return false;
            }
        });
    }
}