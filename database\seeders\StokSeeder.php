<?php

namespace Database\Seeders;

use App\Models\BahanBaku;
use App\Models\Stok;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class StokSeeder extends Seeder
{
    public function run(): void
    {
        $bahanBakus = BahanBaku::all();
        
        // Sample realistic monthly stock patterns for raw materials (non-Ragi)
        $patterns = [
            // Pattern with slight upward trend and seasonal variations
            [120, 135, 128, 145, 138, 152, 147, 160, 155, 168, 162, 175],
            // Pattern with fluctuating demand
            [250, 235, 260, 245, 270, 255, 280, 265, 290, 275, 295, 280],
            // Pattern with stable demand and minor variations
            [180, 185, 175, 182, 178, 184, 179, 186, 181, 188, 183, 190],
        ];

        // Specific pattern for 'Ragi' (yeast) - 12 months, values around 5.0 kg
        // Chronological order: oldest month to most recent historical month
        $ragiPattern = [5.0, 4.8, 5.1, 4.9, 5.2, 5.0, 4.7, 5.3, 5.1, 4.8, 5.2, 5.0];

        // Specific pattern for 'Coklat' (chocolate) - 12 months, based on image
        // Chronological order: Maret (38) to Feb<PERSON>ari (32)
        $coklatPattern = [38, 40, 35, 36, 34, 32, 35, 31, 30, 33, 31, 32];

        // Specific pattern for 'Keju' (cheese) - 12 months, based on image
        // Chronological order: Maret (52) to Februari (43)
        $kejuPattern = [52, 58, 53, 50, 48, 52, 47, 45, 43, 46, 42, 43];

        // Specific pattern for 'Telur' (egg) - 12 months, based on image
        // Chronological order: Maret (68) to Februari (60)
        $telurPattern = [68, 75, 70, 69, 71, 67, 65, 63, 60, 62, 64, 60];

        foreach ($bahanBakus as $index => $bahanBaku) {
            // Generate 12 months of historical data
            for ($i = 11; $i >= 0; $i--) {
                $date = Carbon::now()->subMonths($i)->startOfMonth();
                $jumlahStok = 0.0;

                // Check if the current raw material is 'Ragi' (case-insensitive)
                if (strtolower($bahanBaku->nama) === 'ragi') {
                    // Use the specific pattern for 'Ragi'
                    // The pattern is indexed from 0 (oldest) to 11 (most recent historical)
                    // (11 - i) maps the loop to the pattern index correctly
                    $jumlahStok = $ragiPattern[11 - $i];
                } elseif (strtolower($bahanBaku->nama) === 'coklat') {
                    // Use the specific pattern for 'Coklat'
                    $jumlahStok = $coklatPattern[11 - $i];
                } elseif (strtolower($bahanBaku->nama) === 'keju') {
                    // Use the specific pattern for 'Keju'
                    $jumlahStok = $kejuPattern[11 - $i];
                } elseif (strtolower($bahanBaku->nama) === 'telur') {
                    // Use the specific pattern for 'Telur'
                    $jumlahStok = $telurPattern[11 - $i];
                } else {
                    // Existing logic for other raw materials
                    $currentPattern = $patterns[$index % count($patterns)];
                    $baseValue = $currentPattern[11 - $i];
                    
                    // Add some random variation (±10%)
                    $variation = $baseValue * (rand(-10, 10) / 100);
                    // round() will produce an integer, which becomes X.0 when stored in decimal(10,1)
                    $finalValue = round($baseValue + $variation); 
                    $jumlahStok = max(0, $finalValue);
                }

                Stok::create([
                    'bahan_baku_id' => $bahanBaku->id,
                    'periode' => $date,
                    'jumlah' => $jumlahStok,
                ]);
            }
        }
    }
}