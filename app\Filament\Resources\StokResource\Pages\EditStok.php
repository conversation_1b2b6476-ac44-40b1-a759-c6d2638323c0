<?php

namespace App\Filament\Resources\StokResource\Pages;

use App\Filament\Resources\StokResource;
use App\Models\Stok;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;

class EditStok extends EditRecord
{
    protected static string $resource = StokResource::class;

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        $stokData = $data['stok_data'] ?? [];
        $oldPeriode = $record->periode;
        $newPeriode = \Carbon\Carbon::parse($data['periode'])->startOfMonth();
        
        // If trying to change to a different period
        if ($oldPeriode->format('Y-m') !== $newPeriode->format('Y-m')) {
            // Check if the new period already exists
            $existingRecord = Stok::where('periode', 'LIKE', $newPeriode->format('Y-m').'%')
                ->where('id', '!=', $record->id)
                ->first();
                
            if ($existingRecord) {
                Notification::make()
                    ->danger()
                    ->title('Periode Sudah Ada')
                    ->body('Data stok untuk periode ini sudah ada. Silakan edit data yang ada atau pilih periode lain.')
                    ->persistent()
                    ->send();
                
                $this->halt();
            }
        }
    
        // Update all related records
        foreach ($stokData as $bahanBakuId => $values) {
            Stok::where('periode', $oldPeriode->format('Y-m-d'))
                ->where('bahan_baku_id', $bahanBakuId)
                ->update([
                    'periode' => $newPeriode->format('Y-m-d'),
                    'jumlah' => $values['jumlah'],
                    'satuan' => $values['satuan'],
                    'harga' => $values['harga'] ?? 0
                ]);
        }
    
        return $record->fresh();
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        return $data;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->label('Hapus')
                ->modalHeading('Hapus Data Stok')
                ->modalDescription('Apakah Anda yakin ingin menghapus data ini?')
                ->modalSubmitActionLabel('Ya, Hapus')
                ->modalCancelActionLabel('Batal')
                ->action(function (Model $record) {
                    Stok::where('periode', $record->periode)->forceDelete();
                    
                    // Redirect to the index page after deletion
                    $this->redirect($this->getResource()::getUrl('index'));
                })
                ->successNotificationTitle('Stok berhasil dihapus'),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    public function getTitle(): string 
    {
        return 'Edit Data Stok';
    }

    protected function getSaveFormAction(): Actions\Action
    {
        return parent::getSaveFormAction()
            ->label('Simpan');
    }

    protected function getCancelFormAction(): Actions\Action
    {
        return parent::getCancelFormAction()
            ->label('Batal');
    }
}