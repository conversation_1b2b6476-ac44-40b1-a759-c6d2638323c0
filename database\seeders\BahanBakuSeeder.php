<?php

namespace Database\Seeders;

use App\Models\BahanBaku;
use Illuminate\Database\Seeder;

class BahanBakuSeeder extends Seeder
{
    public function run(): void
    {
        $bahanBakus = [
            ['kode' => 'TT01', 'nama' => 'Tepung Terigu'],
            ['kode' => 'RG01', 'nama' => 'Ragi'],
            ['kode' => 'TL01', 'nama' => 'Telur'],
            ['kode' => 'GL01', 'nama' => 'Gula'],
            ['kode' => 'MG01', 'nama' => 'Margarin'],
            ['kode' => 'KJ01', 'nama' => 'Keju'],
            ['kode' => 'CK01', 'nama' => 'Coklat'],
        ];

        foreach ($bahanBakus as $bahan) {
            BahanBaku::create($bahan);
        }
    }
}