<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laporan Predi<PERSON><PERSON> {{ $bahanBaku->nama }} Periode {{ $data['periode']->translatedFormat('F Y') }}</title>
    <style>
        /* --- Unified Base Styles --- */
        body {
            font-family: 'DejaVu Sans', sans-serif;
            margin: 0;
            padding: 0;
            color: #333;
            font-size: 12px;
        }
        .container {
            padding: 20px;
        }
        .header, .footer {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h1, .header h2 {
            margin: 0 0 5px 0;
            font-size: 20px;
            color: #000;
        }
        .header p {
            margin: 5px 0;
            font-size: 12px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            vertical-align: top;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
            text-align: center;
        }
        /* Utility Classes */
        .text-left { text-align: left; }
        .text-center { text-align: center; }
        .text-right { text-align: right; }
        .bold { font-weight: bold; }
        .no-border { border: none; } /* Utility for layout tables */
        .no-border th, .no-border td { border: none; padding: 5px; }

        /* --- Specific Styles for Prediksi --- */
        .summary-row td { /* Styles for table footer summary rows */
             background-color: #f2f2f2;
             font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>Laporan Prediksi Stok {{ $bahanBaku->nama }}</h2>
            <p>Periode Prediksi: {{ $data['periode']->translatedFormat('F Y') }}</p>
        </div>

        <table>
            <thead>
                <tr>
                    <th style="width: 25%;">Periode Historis</th>
                    <th style="width: 20%;">Aktual</th>
                    <th style="width: 20%;">Prediksi</th>
                    <th style="width: 15%;">Error</th>
                    <th style="width: 20%;">MAPE</th>
                </tr>
            </thead>
            <tbody>
                @php
                    // Reverse the prediction data array to display newest period last in the table body
                    $predictions = array_reverse($data['predictions']);
                @endphp
                @foreach($predictions as $pred)
                    <tr>
                        <td class="text-center">{{ \Carbon\Carbon::parse($pred['periode'])->translatedFormat('F Y') }}</td>
                        <td class="text-left">{{ (float)$pred['aktual'] == (int)$pred['aktual'] ? number_format((float)$pred['aktual'], 0) : number_format((float)$pred['aktual'], 1) }} {{ $pred['satuan'] }}</td>
                        <td class="text-left">{{ $pred['prediksi'] !== null ? ((float)$pred['prediksi'] == (int)$pred['prediksi'] ? number_format((float)$pred['prediksi'], 0) : number_format((float)$pred['prediksi'], 1)) . ' ' . $pred['satuan'] : '-' }}</td>
                        <td class="text-left">{{ $pred['error'] !== null ? number_format($pred['error'], 2) : '-' }}</td>
                        <td class="text-left">{{ $pred['mape'] !== null ? number_format($pred['mape'], 2) . '%' : '-' }}</td>
                    </tr>
                @endforeach
            </tbody>
            <tfoot>
                <tr>
                    <td class="summary-row text-center bold" colspan="4">MAPE (Mean Absolute Percentage Error)</td>
                    <td class="summary-row text-left bold">{{ number_format($data['mape'], 2) }}%</td>
                </tr>
                <tr>
                    <td class="summary-row text-center bold">Hasil Prediksi</td>
                    <td class="summary-row text-center bold">{{ $data['periode']->translatedFormat('F Y') }}</td>
                    <td class="summary-row text-left bold" colspan="3">{{ (float)$data['prediction'] == (int)$data['prediction'] ? number_format((float)$data['prediction'], 0) : number_format((float)$data['prediction'], 1) }} {{ $data['satuan'] }}</td>
                </tr>
            </tfoot>
        </table>
    </div>
</body>
</html>