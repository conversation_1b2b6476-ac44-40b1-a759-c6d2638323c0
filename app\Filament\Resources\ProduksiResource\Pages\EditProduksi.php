<?php

namespace App\Filament\Resources\ProduksiResource\Pages;

use App\Filament\Resources\ProduksiResource;
use App\Models\Produksi;
use Filament\Actions\Action;
use Filament\Actions\DeleteAction;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;

class EditProduksi extends EditRecord
{
    protected static string $resource = ProduksiResource::class;

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        $produksiData = $data['produksi_data'] ?? [];
        $oldPeriode = $record->periode;
        $newPeriode = \Carbon\Carbon::parse($data['periode'])->startOfMonth();

        // If trying to change to a different period
        if ($oldPeriode->format('Y-m') !== $newPeriode->format('Y-m')) {
            // Check if the new period already exists
            $existingRecord = Produksi::where('periode', 'LIKE', $newPeriode->format('Y-m').'%')
                ->where('id', '!=', $record->id)
                ->first();

            if ($existingRecord) {
                Notification::make()
                    ->danger()
                    ->title('Periode Sudah Ada')
                    ->body('Data produksi untuk periode ini sudah ada. Silakan edit data yang ada atau pilih periode lain.')
                    ->persistent()
                    ->send();

                $this->halt();
            }
        }

        // Delete existing records for this period
        Produksi::where('periode', $oldPeriode)->delete();

        // Create new records
        $firstRecord = null;
        foreach ($produksiData as $produkId => $values) {
            $produksi = Produksi::create([
                'periode' => $newPeriode,
                'produk_id' => $produkId,
                'jumlah' => $values['jumlah'],
                'satuan' => $values['satuan'],
            ]);

            if (!$firstRecord) {
                $firstRecord = $produksi;
            }
        }

        return $firstRecord ?? $record;
    }

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make()
                ->label('Hapus')
                ->modalHeading('Hapus Data Produksi')
                ->modalDescription('Apakah Anda yakin ingin menghapus data ini?')
                ->modalSubmitActionLabel('Ya, Hapus')
                ->modalCancelActionLabel('Batal')
                ->action(function () {
                    Produksi::where('periode', $this->record->periode)->delete();
                }),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    public function getTitle(): string
    {
        return 'Edit Data Produksi';
    }

    protected function getSaveFormAction(): Action
    {
        return parent::getSaveFormAction()
            ->label('Simpan');
    }

    protected function getCancelFormAction(): Action
    {
        return parent::getCancelFormAction()
            ->label('Batal');
    }
}